#!/usr/bin/env python3
"""
Test script to verify the implementation of the OpenTax assignment.
This script tests both the ETL pipeline logic and FastAPI endpoints.
"""

import pandas as pd
import requests
import time
import sys
from datetime import datetime

def test_data_transformation():
    """Test the data transformation logic locally"""
    print("🧪 Testing Data Transformation Logic...")
    
    # Sample test data similar to the CSV
    test_data = {
        'transaction_id': ['TXN001', 'TXN002', 'TXN003', 'TXN004', 'TXN001'],  # TXN001 is duplicate
        'user_id': [123, 456, 123, 789, 123],
        'amount': ['$100.50', '-50.25', '', '200', '$100.50'],  # Various formats
        'transaction_date': ['2024-01-15', '01/20/2024', '2024/02/10', '15-03-2024', '2024-01-15']
    }
    
    df = pd.DataFrame(test_data)
    print(f"Original data: {len(df)} records")
    
    # Test amount cleaning
    def clean_amount(amount):
        if pd.isna(amount) or amount == '':
            return 0.0
        amount_str = str(amount).strip().replace('$', '').replace(',', '')
        try:
            return float(amount_str)
        except ValueError:
            return 0.0
    
    df['amount'] = df['amount'].apply(clean_amount)
    print("Amount cleaning successful")
    
    # Test date normalization
    def normalize_date(date_str):
        if pd.isna(date_str):
            return None
        date_str = str(date_str).strip()
        date_formats = ['%Y-%m-%d', '%m/%d/%Y', '%Y/%m/%d', '%d-%m-%Y']
        
        for fmt in date_formats:
            try:
                parsed_date = pd.to_datetime(date_str, format=fmt)
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue
        return None
    
    df['transaction_date'] = df['transaction_date'].apply(normalize_date)
    print("Date normalization successful")
    
    # Test duplicate removal
    initial_count = len(df)
    df = df.drop_duplicates(subset=['transaction_id'], keep='first')
    print(f"Duplicate removal: {initial_count - len(df)} duplicates removed")
    
    print(f"Final cleaned data: {len(df)} records")
    print(df.to_string())
    print()

def test_fastapi_service():
    """Test the FastAPI service endpoints"""
    print("Testing FastAPI Service...")
    
    base_url = "http://localhost:8000"
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("Health check endpoint working")
        else:
            print(f"Health check failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Could not connect to FastAPI service: {e}")
        return False
    
    # Test root endpoint
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("Root endpoint working")
        else:
            print(f"Root endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Root endpoint error: {e}")
    
    # Test users endpoint
    try:
        response = requests.get(f"{base_url}/transactions/users", timeout=10)
        if response.status_code == 200:
            users_data = response.json()
            print(f"Users endpoint working - Found {users_data.get('count', 0)} users")
            
            # Test summary endpoint with first user
            if users_data.get('user_ids') and len(users_data['user_ids']) > 0:
                test_user_id = users_data['user_ids'][0]
                summary_response = requests.get(f"{base_url}/transactions/{test_user_id}/summary", timeout=10)
                
                if summary_response.status_code == 200:
                    summary_data = summary_response.json()
                    print(f"Summary endpoint working for user {test_user_id}")
                    print(f"  - Total transactions: {summary_data.get('total_transactions')}")
                    print(f"  - Total amount: ${summary_data.get('total_amount'):.2f}")
                    print(f"  - Average amount: ${summary_data.get('average_transaction_amount'):.2f}")
                else:
                    print(f"Summary endpoint failed: {summary_response.status_code}")
            else:
                print("No users found - ETL might not have run yet")
        else:
            print(f"Users endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Users endpoint error: {e}")
    
    # Test 404 error handling
    try:
        response = requests.get(f"{base_url}/transactions/999999/summary", timeout=5)
        if response.status_code == 404:
            print("404 error handling working correctly")
        else:
            print(f"Expected 404, got {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"Error testing 404 handling: {e}")
    
    print()

def check_services_status():
    """Check if all required services are running"""
    print("Checking Service Status...")
    
    services = {
        "FastAPI": "http://localhost:8000/health",
        "Airflow": "http://localhost:8080/health"
    }
    
    all_services_up = True
    
    for service_name, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"{service_name} is running")
            else:
                print(f"{service_name} returned status {response.status_code}")
                all_services_up = False
        except requests.exceptions.RequestException:
            print(f" {service_name} is not accessible")
            all_services_up = False
    
    return all_services_up

def main():
    """Main test function"""
    print("OpenTax Assignment Implementation Test")
    print("=" * 50)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Data transformation logic
    test_data_transformation()
    
    # Test 2: Check if services are running
    services_up = check_services_status()
    print()
    
    if not services_up:
        print("  Some services are not running. Make sure to run:")
        print("   docker-compose up -d")
        print("   Wait 2-3 minutes for services to initialize")
        print()
    
    # Test 3: FastAPI service
    test_fastapi_service()
    
    # Summary
    print(" Test Summary:")
    print("- Data transformation logic: Tested locally")
    print("- FastAPI service endpoints: Tested (if services are running)")
    print("- Error handling:  Tested")
    print()
    print(" Next Steps:")
    print("1. Run 'docker-compose up -d' if services aren't running")
    print("2. Access Airflow UI at http://localhost:8080 to run ETL")
    print("3. Test API at http://localhost:8000/docs")
    print("4. Check database optimization guide in DATABASE_OPTIMIZATION.md")

if __name__ == "__main__":
    main()
