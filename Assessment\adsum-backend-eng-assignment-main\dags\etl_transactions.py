from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import logging
import re
from sqlalchemy import create_engine
from airflow import DAG
from airflow.operators.python import PythonOperator

# Define default arguments for the DAG
default_args = {
    'owner': 'candidate',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

# Define the DAG
dag = DAG(
    'etl_transactions',
    default_args=default_args,
    description='ETL pipeline for financial transactions',
    schedule_interval='0 0 * * *',  # Run daily at midnight
    start_date=datetime(2023, 1, 1),
    catchup=False,
)

# Database connection
DATABASE_URL = "******************************************/finance_app"

def extract_transactions(**context):
    """Extract transactions from CSV file"""
    try:
        logging.info("Starting extraction of transactions from CSV")

        # Load CSV file
        df = pd.read_csv('/opt/airflow/data/financial_transactions.csv')

        logging.info(f"Extracted {len(df)} transactions from CSV")
        logging.info(f"Columns: {df.columns.tolist()}")

        # Store raw data for next task
        context['task_instance'].xcom_push(key='raw_data', value=df.to_json())

        return f"Successfully extracted {len(df)} transactions"

    except Exception as e:
        logging.error(f"Error in extraction: {str(e)}")
        raise

def transform_transactions(**context):
    """Transform and clean transaction data"""
    try:
        logging.info("Starting transformation of transactions")

        # Get raw data from previous task
        raw_data_json = context['task_instance'].xcom_pull(key='raw_data', task_ids='extract_task')
        df = pd.read_json(raw_data_json)

        logging.info(f"Starting transformation with {len(df)} records")

        # 1. Clean amount column - convert to float
        def clean_amount(amount):
            if pd.isna(amount) or amount == '':
                return 0.0

            # Convert to string and clean
            amount_str = str(amount).strip()

            # Remove $ sign and commas
            amount_str = amount_str.replace('$', '').replace(',', '')

            try:
                return float(amount_str)
            except ValueError:
                logging.warning(f"Could not convert amount: {amount}")
                return 0.0

        df['amount'] = df['amount'].apply(clean_amount)
        logging.info("Amount column cleaned and converted to float")

        # 2. Normalize date formats to YYYY-MM-DD
        def normalize_date(date_str):
            if pd.isna(date_str):
                return None

            date_str = str(date_str).strip()

            # Try different date formats
            date_formats = [
                '%Y-%m-%d',      # 2024-06-28
                '%m/%d/%Y',      # 10/11/2024
                '%Y/%m/%d',      # 2024/06/30
                '%d-%m-%Y',      # 28-06-2024
                '%Y-%m-%d %H:%M:%S'  # In case there are timestamps
            ]

            for fmt in date_formats:
                try:
                    parsed_date = pd.to_datetime(date_str, format=fmt)
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    continue

            # If none of the formats work, try pandas auto-parsing
            try:
                parsed_date = pd.to_datetime(date_str)
                return parsed_date.strftime('%Y-%m-%d')
            except:
                logging.warning(f"Could not parse date: {date_str}")
                return None

        df['transaction_date'] = df['transaction_date'].apply(normalize_date)

        # Remove rows with invalid dates
        initial_count = len(df)
        df = df.dropna(subset=['transaction_date'])
        logging.info(f"Date normalization complete. Removed {initial_count - len(df)} rows with invalid dates")

        # 3. Remove duplicate transactions based on transaction_id
        initial_count = len(df)
        df = df.drop_duplicates(subset=['transaction_id'], keep='first')
        duplicates_removed = initial_count - len(df)
        logging.info(f"Removed {duplicates_removed} duplicate transactions")

        # 4. Additional data quality checks
        # Remove rows with missing transaction_id or user_id
        df = df.dropna(subset=['transaction_id', 'user_id'])

        # Ensure user_id is integer
        df['user_id'] = df['user_id'].astype(int)

        logging.info(f"Transformation complete. Final dataset has {len(df)} records")

        # Store cleaned data for next task
        context['task_instance'].xcom_push(key='cleaned_data', value=df.to_json())

        return f"Successfully transformed {len(df)} transactions"

    except Exception as e:
        logging.error(f"Error in transformation: {str(e)}")
        raise

def load_transactions(**context):
    """Load cleaned transactions into PostgreSQL"""
    try:
        logging.info("Starting load of transactions to PostgreSQL")

        # Get cleaned data from previous task
        cleaned_data_json = context['task_instance'].xcom_pull(key='cleaned_data', task_ids='transform_task')
        df = pd.read_json(cleaned_data_json)

        logging.info(f"Loading {len(df)} transactions to database")

        # Create database connection
        engine = create_engine(DATABASE_URL)

        # Load data to PostgreSQL with conflict handling
        # Use 'replace' to handle any existing data, or 'append' for incremental loads
        df.to_sql(
            'transactions',
            engine,
            if_exists='append',  # Change to 'replace' if you want to replace all data
            index=False,
            method='multi'  # For better performance with large datasets
        )

        logging.info(f"Successfully loaded {len(df)} transactions to database")

        # Log some statistics for auditing
        total_amount = df['amount'].sum()
        avg_amount = df['amount'].mean()
        unique_users = df['user_id'].nunique()

        logging.info(f"Load statistics - Total amount: {total_amount:.2f}, "
                    f"Average amount: {avg_amount:.2f}, Unique users: {unique_users}")

        return f"Successfully loaded {len(df)} transactions"

    except Exception as e:
        logging.error(f"Error in loading: {str(e)}")
        raise

# Define tasks
extract_task = PythonOperator(
    task_id='extract_task',
    python_callable=extract_transactions,
    dag=dag,
)

transform_task = PythonOperator(
    task_id='transform_task',
    python_callable=transform_transactions,
    dag=dag,
)

load_task = PythonOperator(
    task_id='load_task',
    python_callable=load_transactions,
    dag=dag,
)

# Set task dependencies
extract_task >> transform_task >> load_task