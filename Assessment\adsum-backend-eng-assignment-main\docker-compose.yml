services:
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow
      - POSTGRES_DB=airflow
    ports:
      - "5432:5432"
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 5s
      retries: 5
    restart: always

  webserver:
    image: apache/airflow:2.6.1
    environment:
      - LOAD_EX=n
      - EXECUTOR=Local
      - AIRFLOW__CORE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres:5432/airflow
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres:5432/airflow
    volumes:
      - ./dags:/opt/airflow/dags
      - ./data:/opt/airflow/data
      - ./logs:/opt/airflow/logs
    ports:
      - "8080:8080"
    command: >
      bash -c "
      echo 'Waiting for PostgreSQL...' &&
      while ! pg_isready -h postgres -p 5432 -U airflow; do sleep 2; done &&
      echo 'Initializing Airflow...' &&
      airflow db init &&
      echo 'Creating admin user...' &&
      (airflow users create --username admin --password admin --firstname Admin --lastname User --role Admin --email <EMAIL> || echo 'User exists') &&
      echo 'Starting services...' &&
      airflow scheduler &
      exec airflow webserver
      "
    healthcheck:
      test: ["CMD-SHELL", "[ -f /opt/airflow/airflow-webserver.pid ]"]
      interval: 30s
      timeout: 30s
      retries: 5
    depends_on:
      postgres:
        condition: service_healthy
    restart: always

  fastapi:
    build:
      context: ./fastapi
      dockerfile: Dockerfile
    volumes:
      - ./fastapi:/app
    ports:
      - "8000:8000"
    depends_on:
      - postgres
    restart: always

  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    depends_on:
      - postgres
    restart: always

volumes:
  postgres-db-volume: