-- OpenTax Financial Transactions Database Schema
-- This script sets up the complete database schema for the financial transactions system

-- Create the main database (if not exists)
-- Note: This is handled by docker-compose and init-db.sql

-- Connect to the finance_app database
-- \c finance_app;

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    amount FLOAT NOT NULL,
    transaction_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_user_date ON transactions(user_id, transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_txn_id ON transactions(transaction_id);

-- Create a partial index for recent transactions (performance optimization)
CREATE INDEX IF NOT EXISTS idx_transactions_recent 
ON transactions(user_id, transaction_date) 
WHERE transaction_date >= CURRENT_DATE - INTERVAL '1 year';

-- Create audit table for ETL logging
CREATE TABLE IF NOT EXISTS etl_audit_log (
    id SERIAL PRIMARY KEY,
    dag_id VARCHAR(100) NOT NULL,
    task_id VARCHAR(100) NOT NULL,
    execution_date TIMESTAMP NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL, -- 'running', 'success', 'failed'
    records_processed INT,
    records_inserted INT,
    records_updated INT,
    records_failed INT,
    error_message TEXT,
    metadata JSONB
);

-- Create index for audit log queries
CREATE INDEX IF NOT EXISTS idx_etl_audit_execution_date ON etl_audit_log(execution_date);
CREATE INDEX IF NOT EXISTS idx_etl_audit_status ON etl_audit_log(status);

-- Create materialized view for user transaction summaries (performance optimization)
CREATE MATERIALIZED VIEW IF NOT EXISTS user_transaction_summaries AS
SELECT 
    user_id,
    COUNT(*) as total_transactions,
    SUM(amount) as total_amount,
    AVG(amount) as average_transaction_amount,
    MIN(transaction_date) as first_transaction_date,
    MAX(transaction_date) as last_transaction_date,
    MIN(amount) as min_transaction_amount,
    MAX(amount) as max_transaction_amount,
    COUNT(CASE WHEN amount > 0 THEN 1 END) as positive_transactions,
    COUNT(CASE WHEN amount < 0 THEN 1 END) as negative_transactions,
    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_credits,
    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_debits
FROM transactions
GROUP BY user_id;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_summaries_user_id ON user_transaction_summaries(user_id);

-- Create function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_user_summaries()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW user_transaction_summaries;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for transactions table
CREATE TRIGGER IF NOT EXISTS update_transactions_updated_at
    BEFORE UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (optional - remove in production)
-- This data will be replaced by the ETL pipeline
INSERT INTO transactions (transaction_id, user_id, amount, transaction_date) VALUES
    ('SAMPLE_001', 1, 100.50, '2024-01-15'),
    ('SAMPLE_002', 1, -25.00, '2024-01-16'),
    ('SAMPLE_003', 2, 500.00, '2024-01-17'),
    ('SAMPLE_004', 2, -100.25, '2024-01-18'),
    ('SAMPLE_005', 3, 75.75, '2024-01-19')
ON CONFLICT (transaction_id) DO NOTHING;

-- Create view for transaction analytics
CREATE OR REPLACE VIEW transaction_analytics AS
SELECT 
    DATE_TRUNC('month', transaction_date) as month,
    COUNT(*) as total_transactions,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(amount) as total_amount,
    AVG(amount) as average_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount,
    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_credits,
    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_debits
FROM transactions
GROUP BY DATE_TRUNC('month', transaction_date)
ORDER BY month;

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON transactions TO airflow;
-- GRANT SELECT ON user_transaction_summaries TO airflow;
-- GRANT SELECT, INSERT ON etl_audit_log TO airflow;

-- Performance monitoring queries (for reference)
/*
-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'transactions';

-- Check table statistics
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup, n_dead_tup
FROM pg_stat_user_tables 
WHERE tablename = 'transactions';

-- Sample queries for testing performance
SELECT user_id, COUNT(*), SUM(amount), AVG(amount) 
FROM transactions 
WHERE user_id = 123;

SELECT * FROM user_transaction_summaries WHERE user_id = 123;

SELECT * FROM transaction_analytics WHERE month >= '2024-01-01';
*/

-- Comments for maintenance
COMMENT ON TABLE transactions IS 'Main table storing financial transaction data processed by ETL pipeline';
COMMENT ON TABLE etl_audit_log IS 'Audit log for ETL pipeline execution tracking';
COMMENT ON MATERIALIZED VIEW user_transaction_summaries IS 'Pre-computed user transaction summaries for fast API responses';
COMMENT ON INDEX idx_transactions_user_id IS 'Primary index for user-based queries';
COMMENT ON INDEX idx_transactions_date IS 'Index for date-based queries and reporting';
COMMENT ON INDEX idx_transactions_user_date IS 'Composite index for user + date queries';
COMMENT ON INDEX idx_transactions_recent IS 'Partial index for recent transaction queries';
